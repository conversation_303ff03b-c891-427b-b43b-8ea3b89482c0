use crate::{
    encoding::{
        book_ticker::FuturesBookTicker,
        futures_order::OrderSide,
        gate::{self, GateBookTicker},
    },
    utils::perf::system_now_in_ms,
};

#[derive(<PERSON>ialEq, Eq, <PERSON><PERSON><PERSON>rd, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Debug)]
enum OpportunityStatus {
    NoDiff,
    BNExchangeDiff,
    GateExchangeDiff,
    ComfirmedDiff,
    Opening,
    Opened,
    OneWayFilled,
    Closing,
}

impl std::fmt::Display for OpportunityStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        let str = match self {
            OpportunityStatus::NoDiff => "NoDiff",
            OpportunityStatus::BNExchangeDiff => "BNExchangeDiff",
            OpportunityStatus::GateExchangeDiff => "GateExchangeDiff",
            OpportunityStatus::ComfirmedDiff => "ComfirmedDiff",
            OpportunityStatus::Opening => "Opening",
            OpportunityStatus::Opened => "Opened",
            OpportunityStatus::OneWayFilled => "OneWayFilled",
            OpportunityStatus::Closing => "Closing",
        };
        write!(f, "{}", str)
    }
}

pub struct Opportunity {
    pub ts: u64,
    pub side: OrderSide,
    pub price: f64,
    pub bn_mid_price: f64,
    pub gate_bid_price: f64,
    pub gate_ask_price: f64,
    take_profit_price: f64,
    status: OpportunityStatus,
}

impl std::fmt::Display for Opportunity {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(
            f,
            r#"
Opportunity:
    side: {}
    price: {}
    take_profit_price: {}
    status: {}
    bn_mid_price: {}
    gate_bid_price: {}
    gate_ask_price: {}
"#,
            self.side.to_string(),
            self.price,
            self.take_profit_price,
            self.status,
            self.bn_mid_price,
            self.gate_bid_price,
            self.gate_ask_price,
        )
    }
}

impl Opportunity {
    fn new() -> Self {
        Self {
            ts: 0,
            side: OrderSide::Buy,
            price: 0.0,
            take_profit_price: 0.0,
            status: OpportunityStatus::NoDiff,
            bn_mid_price: 0.0,
            gate_bid_price: 0.0,
            gate_ask_price: 0.0,
        }
    }

    fn confirm_diff(&self) -> bool {
        self.status == OpportunityStatus::ComfirmedDiff
    }

    fn should_check(&self) -> bool {
        self.status == OpportunityStatus::NoDiff
            || self.status == OpportunityStatus::BNExchangeDiff
            || self.status == OpportunityStatus::GateExchangeDiff
    }

    fn reset_ts(&mut self) {
        self.ts = system_now_in_ms();
    }
}

pub struct ArbitrageGate {
    pub bn_bbo: Option<FuturesBookTicker>,
    pub gate_bbo: Option<GateBookTicker>,
    pub opportunity: Opportunity,
}

impl ArbitrageGate {
    pub fn new() -> Self {
        Self {
            bn_bbo: None,
            gate_bbo: None,
            opportunity: Opportunity::new(),
        }
    }

    pub fn update_bn_bbo(&mut self, bbo: FuturesBookTicker) -> Option<(OrderSide, f64)> {
        self.bn_bbo = Some(bbo);
        if !self.check_price_diff(true) {
            return None;
        }
        self.opportunity.reset_ts();
        if self.opportunity.confirm_diff() {
            crate::info!("execute opportunity {}", self.opportunity);
            self.opportunity.status = OpportunityStatus::Opening;
            Some((self.opportunity.side, self.opportunity.price))
        } else {
            None
        }
    }

    pub fn update_gate_bbo(&mut self, bbo: GateBookTicker) -> Option<&Opportunity> {
        self.gate_bbo = Some(bbo);
        if !self.check_price_diff(false) {
            return None;
        }
        self.opportunity.reset_ts();
        if self.opportunity.confirm_diff() {
            crate::info!("execute opportunity {}", self.opportunity);
            self.opportunity.status = OpportunityStatus::Opening;
            Some(&self.opportunity)
        } else {
            None
        }
    }

    pub fn check_price_diff(&mut self, from_bn: bool) -> bool {
        if !self.opportunity.should_check() {
            return false;
        }
        let now = system_now_in_ms();
        if now - self.opportunity.ts > 5 {
            self.opportunity.status = OpportunityStatus::NoDiff;
            self.opportunity.reset_ts();
        }
        if let (Some(bn_bbo), Some(gate_bbo)) = (&self.bn_bbo, &self.gate_bbo) {
            let bn_mid_price = (bn_bbo.bid_price + bn_bbo.ask_price) / 2.0;
            let diff_ratio_buy = (bn_mid_price - gate_bbo.ask_price) / gate_bbo.ask_price;
            let diff_ratio_sell = (gate_bbo.bid_price - bn_mid_price) / bn_mid_price;
            if diff_ratio_buy > 0.001 {
                self.opportunity.bn_mid_price = bn_mid_price;
                self.opportunity.gate_bid_price = gate_bbo.bid_price;
                self.opportunity.gate_ask_price = gate_bbo.ask_price;
                self.opportunity.side = OrderSide::Buy;
                self.opportunity.price = gate_bbo.ask_price;
                self.opportunity.take_profit_price = bn_mid_price;
                match (from_bn, self.opportunity.status) {
                    (true, OpportunityStatus::NoDiff) => {
                        self.opportunity.status = OpportunityStatus::BNExchangeDiff;
                    }
                    (false, OpportunityStatus::BNExchangeDiff) => {
                        self.opportunity.status = OpportunityStatus::ComfirmedDiff;
                    }
                    (true, OpportunityStatus::GateExchangeDiff) => {
                        self.opportunity.status = OpportunityStatus::ComfirmedDiff;
                    }
                    (false, OpportunityStatus::NoDiff) => {
                        self.opportunity.status = OpportunityStatus::GateExchangeDiff;
                    }
                    _ => {}
                }
            } else if diff_ratio_sell > 0.001 {
                self.opportunity.side = OrderSide::Sell;
                self.opportunity.price = gate_bbo.bid_price;
                self.opportunity.take_profit_price = bn_mid_price;
                self.opportunity.gate_ask_price = gate_bbo.ask_price;
                self.opportunity.gate_bid_price = gate_bbo.bid_price;
                self.opportunity.bn_mid_price = bn_mid_price;
                match (from_bn, self.opportunity.status) {
                    (true, OpportunityStatus::NoDiff) => {
                        self.opportunity.status = OpportunityStatus::BNExchangeDiff;
                    }
                    (false, OpportunityStatus::BNExchangeDiff) => {
                        self.opportunity.status = OpportunityStatus::ComfirmedDiff;
                    }
                    (true, OpportunityStatus::GateExchangeDiff) => {
                        self.opportunity.status = OpportunityStatus::ComfirmedDiff;
                    }
                    (false, OpportunityStatus::NoDiff) => {
                        self.opportunity.status = OpportunityStatus::GateExchangeDiff;
                    }
                    _ => {}
                }
            }
        }
        true
    }

    pub fn update_order_status(
        &mut self,
        status: gate::UnifiedOrderStatus,
    ) -> Option<(f64, OrderSide)> {
        match status {
            gate::UnifiedOrderStatus::Open
                if self.opportunity.status == OpportunityStatus::Opening =>
            {
                crate::info!("order opened");
                self.opportunity.status = OpportunityStatus::Opened;
                None
            }
            gate::UnifiedOrderStatus::Open
                if self.opportunity.status == OpportunityStatus::OneWayFilled =>
            {
                crate::info!("close order opened");
                self.opportunity.status = OpportunityStatus::Closing;
                None
            }
            gate::UnifiedOrderStatus::Filled
                if self.opportunity.status == OpportunityStatus::Opened
                    || self.opportunity.status == OpportunityStatus::Opening =>
            {
                crate::info!("order filled");
                self.opportunity.status = OpportunityStatus::OneWayFilled;
                Some((
                    self.opportunity.take_profit_price,
                    self.opportunity.side.reverse(),
                ))
            }
            gate::UnifiedOrderStatus::Filled
                if self.opportunity.status == OpportunityStatus::Closing =>
            {
                crate::info!("close order filled");
                self.opportunity.status = OpportunityStatus::NoDiff;
                None
            }
            gate::UnifiedOrderStatus::Canceled | gate::UnifiedOrderStatus::Expired
                if self.opportunity.status == OpportunityStatus::Opened
                    || self.opportunity.status == OpportunityStatus::Opening =>
            {
                crate::info!("order canceled");
                self.opportunity.status = OpportunityStatus::NoDiff;
                None
            }
            gate::UnifiedOrderStatus::Canceled | gate::UnifiedOrderStatus::Expired
                if self.opportunity.status == OpportunityStatus::OneWayFilled =>
            {
                crate::info!("close order canceled");
                self.opportunity.status = OpportunityStatus::Closing;
                let price = if self.opportunity.side == OrderSide::Buy {
                    self.gate_bbo.as_ref().unwrap().bid_price
                } else {
                    self.gate_bbo.as_ref().unwrap().ask_price
                };
                Some((price, self.opportunity.side.reverse()))
            }
            _ => None,
        }
    }
}
