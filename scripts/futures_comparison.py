#!/usr/bin/env python3
"""
筛选Binance和Gate都交易的永续合约，按交易量排序并计算price tick占比
"""

import requests
import json
import pandas as pd
import time
from typing import Dict, List, Tuple, Optional
import sys

# 代理配置
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}

def get_binance_futures_info() -> Dict[str, Dict]:
    """获取Binance永续合约信息"""
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    
    try:
        print("正在获取Binance永续合约信息...")
        response = requests.get(url, proxies=PROXIES, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        futures_info = {}
        for symbol_info in data['symbols']:
            if (symbol_info['status'] == 'TRADING' and 
                symbol_info['contractType'] == 'PERPETUAL' and
                symbol_info['quoteAsset'] == 'USDT'):
                
                symbol = symbol_info['symbol']
                
                # 提取价格过滤器信息
                price_tick = None
                lot_size = None
                min_qty = None
                min_notional = None
                
                for filter_info in symbol_info['filters']:
                    if filter_info['filterType'] == 'PRICE_FILTER':
                        price_tick = float(filter_info['tickSize'])
                    elif filter_info['filterType'] == 'LOT_SIZE':
                        lot_size = float(filter_info['stepSize'])
                        min_qty = float(filter_info['minQty'])
                    elif filter_info['filterType'] == 'MIN_NOTIONAL':
                        min_notional = float(filter_info['notional'])
                
                futures_info[symbol] = {
                    'baseAsset': symbol_info['baseAsset'],
                    'quoteAsset': symbol_info['quoteAsset'],
                    'priceTick': price_tick,
                    'lotSize': lot_size,
                    'minQty': min_qty,
                    'minNotional': min_notional,
                    'status': symbol_info['status']
                }
        
        print(f"获取到 {len(futures_info)} 个Binance永续合约")
        return futures_info
        
    except Exception as e:
        print(f"获取Binance永续合约信息失败: {e}")
        return {}

def get_binance_futures_24hr_stats() -> Dict[str, Dict]:
    """获取Binance永续合约24小时统计数据"""
    url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
    
    try:
        print("正在获取Binance永续合约24小时统计数据...")
        response = requests.get(url, proxies=PROXIES, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        stats = {}
        for item in data:
            symbol = item['symbol']
            stats[symbol] = {
                'volume': float(item['volume']),
                'quoteVolume': float(item['quoteVolume']),
                'count': int(item['count']),
                'lastPrice': float(item['lastPrice'])
            }
        
        print(f"获取到 {len(stats)} 个合约的24小时统计数据")
        return stats
        
    except Exception as e:
        print(f"获取Binance 24小时统计数据失败: {e}")
        return {}

def get_gate_futures_contracts() -> Dict[str, Dict]:
    """获取Gate永续合约信息"""
    url = "https://api.gateio.ws/api/v4/futures/usdt/contracts"
    
    try:
        print("正在获取Gate永续合约信息...")
        response = requests.get(url, proxies=PROXIES, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        contracts = {}
        for contract in data:
            if contract['type'] == 'direct' and contract['quanto_multiplier'] == '1':
                symbol = contract['name']  # 例如: BTC_USDT
                
                # 转换为Binance格式的symbol (去掉下划线)
                binance_symbol = symbol.replace('_', '')
                
                contracts[binance_symbol] = {
                    'name': symbol,
                    'underlying': contract['underlying'],
                    'quote_currency': contract['quote_currency'],
                    'settle_currency': contract['settle_currency'],
                    'leverage_min': float(contract['leverage_min']),
                    'leverage_max': float(contract['leverage_max']),
                    'mark_price': float(contract['mark_price']) if contract['mark_price'] else 0,
                    'index_price': float(contract['index_price']) if contract['index_price'] else 0,
                    'order_price_round': float(contract['order_price_round']),
                    'order_size_min': int(contract['order_size_min']),
                    'order_size_max': int(contract['order_size_max'])
                }
        
        print(f"获取到 {len(contracts)} 个Gate永续合约")
        return contracts
        
    except Exception as e:
        print(f"获取Gate永续合约信息失败: {e}")
        return {}

def get_gate_futures_tickers() -> Dict[str, Dict]:
    """获取Gate永续合约行情数据"""
    url = "https://api.gateio.ws/api/v4/futures/usdt/tickers"
    
    try:
        print("正在获取Gate永续合约行情数据...")
        response = requests.get(url, proxies=PROXIES, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        tickers = {}
        for ticker in data:
            symbol = ticker['contract']  # 例如: BTC_USDT
            binance_symbol = symbol.replace('_', '')
            
            tickers[binance_symbol] = {
                'contract': symbol,
                'last': float(ticker['last']) if ticker['last'] else 0,
                'volume_24h': float(ticker['volume_24h']) if ticker['volume_24h'] else 0,
                'volume_24h_base': float(ticker['volume_24h_base']) if ticker['volume_24h_base'] else 0,
                'volume_24h_quote': float(ticker['volume_24h_quote']) if ticker['volume_24h_quote'] else 0,
                'volume_24h_settle': float(ticker['volume_24h_settle']) if ticker['volume_24h_settle'] else 0
            }
        
        print(f"获取到 {len(tickers)} 个合约的行情数据")
        return tickers
        
    except Exception as e:
        print(f"获取Gate行情数据失败: {e}")
        return {}

def calculate_price_tick_ratio(price: float, tick_size: float) -> float:
    """计算price tick占比"""
    if price <= 0 or tick_size <= 0:
        return 0
    return (tick_size / price) * 100

def main():
    print("开始获取Binance和Gate永续合约数据...")
    
    # 获取Binance数据
    binance_futures = get_binance_futures_info()
    binance_stats = get_binance_futures_24hr_stats()
    
    # 获取Gate数据
    gate_contracts = get_gate_futures_contracts()
    gate_tickers = get_gate_futures_tickers()
    
    if not binance_futures or not gate_contracts:
        print("获取数据失败，退出程序")
        return
    
    # 找出两个交易所都有的永续合约
    common_symbols = set(binance_futures.keys()) & set(gate_contracts.keys())
    print(f"\n找到 {len(common_symbols)} 个两个交易所都有的永续合约")
    
    # 准备数据
    results = []
    
    for symbol in common_symbols:
        binance_info = binance_futures[symbol]
        gate_info = gate_contracts[symbol]
        
        # 获取交易量数据
        binance_volume = 0
        binance_quote_volume = 0
        binance_price = 0
        
        if symbol in binance_stats:
            binance_volume = binance_stats[symbol]['volume']
            binance_quote_volume = binance_stats[symbol]['quoteVolume']
            binance_price = binance_stats[symbol]['lastPrice']
        
        gate_volume = 0
        gate_quote_volume = 0
        gate_price = 0
        
        if symbol in gate_tickers:
            gate_volume = gate_tickers[symbol]['volume_24h_base']
            gate_quote_volume = gate_tickers[symbol]['volume_24h_quote']
            gate_price = gate_tickers[symbol]['last']
        
        # 计算price tick占比
        binance_tick_ratio = 0
        if binance_price > 0 and binance_info['priceTick']:
            binance_tick_ratio = calculate_price_tick_ratio(binance_price, binance_info['priceTick'])
        
        gate_tick_ratio = 0
        if gate_price > 0:
            gate_tick_ratio = calculate_price_tick_ratio(gate_price, gate_info['order_price_round'])
        
        results.append({
            'symbol': symbol,
            'base_asset': binance_info['baseAsset'],
            'binance_volume_24h': binance_volume,
            'binance_quote_volume_24h': binance_quote_volume,
            'binance_price': binance_price,
            'binance_price_tick': binance_info['priceTick'],
            'binance_tick_ratio_pct': binance_tick_ratio,
            'gate_volume_24h': gate_volume,
            'gate_quote_volume_24h': gate_quote_volume,
            'gate_price': gate_price,
            'gate_price_tick': gate_info['order_price_round'],
            'gate_tick_ratio_pct': gate_tick_ratio,
            'total_quote_volume': binance_quote_volume + gate_quote_volume
        })
    
    # 按总交易量排序（从小到大）
    results.sort(key=lambda x: x['total_quote_volume'])
    
    # 创建DataFrame并保存
    df = pd.DataFrame(results)
    
    # 格式化数值显示
    pd.set_option('display.float_format', '{:.8f}'.format)
    
    print(f"\n共找到 {len(results)} 个两个交易所都有的永续合约")
    print("\n按总交易量从小到大排序的结果:")
    print("=" * 120)
    
    # 显示前20个结果
    for i, row in enumerate(results[:20], 1):
        print(f"{i:2d}. {row['symbol']:12s} | "
              f"总量: ${row['total_quote_volume']:>15,.0f} | "
              f"BN: ${row['binance_quote_volume_24h']:>12,.0f} ({row['binance_tick_ratio_pct']:>6.4f}%) | "
              f"Gate: ${row['gate_quote_volume_24h']:>12,.0f} ({row['gate_tick_ratio_pct']:>6.4f}%)")
    
    # 保存到CSV文件
    output_file = 'scripts/binance_gate_futures_comparison.csv'
    df.to_csv(output_file, index=False)
    print(f"\n完整结果已保存到: {output_file}")
    
    # 统计信息
    print(f"\n统计信息:")
    print(f"- 总共对比合约数: {len(results)}")
    print(f"- 平均Binance tick占比: {df['binance_tick_ratio_pct'].mean():.6f}%")
    print(f"- 平均Gate tick占比: {df['gate_tick_ratio_pct'].mean():.6f}%")
    print(f"- 总交易量范围: ${df['total_quote_volume'].min():,.0f} - ${df['total_quote_volume'].max():,.0f}")

if __name__ == "__main__":
    main()
